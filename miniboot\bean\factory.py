#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 分层Bean工厂实现 - 重构后的DefaultListableBeanFactory
"""

import asyncio
import threading
import time
from contextlib import contextmanager
from typing import Any, Optional, TypeVar

from loguru import logger

from .base import BeanDefinitionRegistry, BeanFactory, ConfigurableBeanFactory, ListableBeanFactory
from .cache import CacheManager
from .definition import BeanDefinition
from .registry import DefaultBeanDefinitionRegistry
from .scopes import ScopeManager

T = TypeVar("T")


class AsyncContextDetector:
    """异步上下文检测器

    提供异步环境检测和Bean异步处理需求分析功能.
    """

    @staticmethod
    def is_async_context_active() -> bool:
        """检测当前是否在活跃的异步上下文中

        Returns:
            bool: 如果在异步上下文中返回True, 否则返回False
        """
        try:
            # 检查是否有运行中的事件循环
            loop = asyncio.get_running_loop()
            return loop is not None
        except RuntimeError:
            return False

    @staticmethod
    def should_use_async_processing(bean_name: str) -> bool:
        """判断Bean是否应该使用异步处理

        Args:
            bean_name: Bean名称

        Returns:
            bool: 如果应该使用异步处理返回True
        """
        # 基于Bean名称模式判断是否为I/O密集型
        io_intensive_patterns = ["database", "db", "connection", "client", "service", "repository", "dao", "api", "http", "redis", "cache"]

        bean_name_lower = bean_name.lower()
        return any(pattern in bean_name_lower for pattern in io_intensive_patterns)

    @staticmethod
    def detect_execution_mode() -> str:
        """检测当前执行模式

        Returns:
            str: 'async' 或 'sync'
        """
        return "async" if AsyncContextDetector.is_async_context_active() else "sync"

    @staticmethod
    def get_event_loop_info() -> dict[str, any]:
        """获取事件循环信息

        Returns:
            事件循环的详细信息
        """
        try:
            loop = asyncio.get_running_loop()
            return {"has_loop": True, "loop_type": type(loop).__name__, "is_running": loop.is_running(), "is_closed": loop.is_closed()}
        except RuntimeError:
            return {"has_loop": False, "loop_type": None, "is_running": False, "is_closed": True}

    @staticmethod
    def is_coroutine_function(func) -> bool:
        """检测函数是否为协程函数

        Args:
            func: 要检测的函数

        Returns:
            bool: 如果是协程函数返回True
        """
        return asyncio.iscoroutinefunction(func)


class DefaultListableBeanFactory(ConfigurableBeanFactory, ListableBeanFactory):
    """默认Bean工厂 - 内置异步适配

    这是重构后的核心Bean工厂实现，采用分层架构设计：
    1. 职责分离：将缓存、注入、生命周期等功能分离到专门的管理器
    2. 异步适配：自动检测执行环境并选择最优方式
    3. 分层继承：实现完整的Bean工厂接口层次结构
    """

    def __init__(self, registry: Optional[BeanDefinitionRegistry] = None):
        """初始化DefaultListableBeanFactory

        Args:
            registry: Bean定义注册表，如果为None则创建默认实现
        """
        # 初始化依赖组件
        if registry is None:
            registry = DefaultBeanDefinitionRegistry()

        self._registry = registry
        self._parent_bean_factory: Optional[BeanFactory] = None

        # 分离的管理器组件
        self._cache_manager = CacheManager()
        self._scope_manager = ScopeManager()
        self._async_detector = AsyncContextDetector()

        # Bean后置处理器管理（直接在工厂中管理，符合Spring Boot设计）
        self._bean_post_processors: list[Any] = []

        # 单例Bean存储
        self._singletons: dict[str, Any] = {}

        # 线程安全锁
        self._lock = threading.RLock()

        # 性能监控统计
        self._performance_stats = {"total_beans_created": 0, "cache_hits": 0, "cache_misses": 0, "total_creation_time": 0.0}

    def get_bean(self, name: str, required_type: Optional[type] = None) -> Any:
        """获取Bean实例 - 自动适配同步/异步执行环境

        这是核心方法，自动检测执行环境并选择最优方式：
        1. 在异步环境中返回awaitable对象
        2. 在同步环境中直接返回Bean实例
        3. 根据Bean特性选择同步或异步处理

        Args:
            name: Bean名称
            required_type: 期望的Bean类型

        Returns:
            Bean实例或awaitable对象
        """
        # 检测当前执行环境
        if self._async_detector.is_async_context_active():
            # 在异步环境中, 返回awaitable对象
            return self._get_async(name, required_type)
        elif self._async_detector.should_use_async_processing(name):
            # 同步环境但Bean需要异步处理
            return asyncio.run(self._get_async(name, required_type))
        else:
            # 同步环境且Bean可同步处理
            return self._get_sync(name, required_type)

    def _get_sync(self, name: str, required_type: Optional[type] = None) -> Any:
        """同步Bean获取内部实现"""
        with self._lock:
            # 1. 检查缓存
            cached_bean = self._cache_manager.get_bean(name)
            if cached_bean:
                self._performance_stats["cache_hits"] += 1
                if required_type and not isinstance(cached_bean, required_type):
                    raise TypeError(f"Bean '{name}' is not of required type {required_type}")
                return cached_bean

            self._performance_stats["cache_misses"] += 1

            # 2. 检查单例
            if name in self._singletons:
                singleton = self._singletons[name]
                if required_type and not isinstance(singleton, required_type):
                    raise TypeError(f"Bean '{name}' is not of required type {required_type}")
                return singleton

            # 3. 检查Bean定义
            if not self._registry.has_definition(name):
                # 检查父工厂
                if self._parent_bean_factory and self._parent_bean_factory.contains_bean(name):
                    return self._parent_bean_factory.get_bean(name, required_type)
                raise KeyError(f"No bean definition found for name: {name}")

            # 4. 创建Bean
            bean_definition = self._registry.get_definition(name)
            bean = self._create_sync(name, bean_definition)

            # 5. 类型检查
            if required_type and not isinstance(bean, required_type):
                raise TypeError(f"Bean '{name}' is not of required type {required_type}")

            return bean

    async def _get_async(self, name: str, required_type: Optional[type] = None) -> Any:
        """异步Bean获取内部实现"""
        # 异步版本的Bean获取逻辑
        # 这里可以实现异步的Bean创建和依赖注入
        return self._get_sync(name, required_type)

    def _create_sync(self, name: str, bean_definition: BeanDefinition) -> Any:
        """同步创建Bean"""
        start_time = time.time()

        try:
            # 1. 实例化Bean
            bean = self._instantiate_bean(bean_definition)

            # 2. 属性填充和依赖注入 (这里应该有依赖注入逻辑)
            # TODO: 添加属性注入逻辑

            # 3. 执行前置处理器 (在初始化方法之前)
            for processor in self._bean_post_processors:
                bean = processor.pre_process(bean, name)

            # 5. 执行后置处理器 (在初始化方法之后)
            for processor in self._bean_post_processors:
                bean = processor.post_process(bean, name)

            # 6. 缓存Bean（如果是单例）
            if bean_definition.singleton():
                self._singletons[name] = bean
                self._cache_manager.put_bean(name, bean)

            # 7. 性能统计
            creation_time = time.time() - start_time
            self._performance_stats["total_beans_created"] += 1
            self._performance_stats["total_creation_time"] += creation_time

            logger.debug(f"Created bean '{name}' in {creation_time:.3f}s")
            return bean

        except Exception as e:
            logger.error(f"Failed to create bean '{name}': {e}")
            raise

    def _instantiate_bean(self, bean_definition: BeanDefinition) -> Any:
        """实例化Bean"""
        bean_class = bean_definition.bean_class
        if bean_class is None:
            raise ValueError(f"Bean class is None for definition: {bean_definition}")

        # 简单实例化（后续可以扩展支持构造函数参数）
        return bean_class()

    # ===== BeanFactory接口实现 =====

    def contains_bean(self, name: str) -> bool:
        """检查Bean是否存在"""
        # 检查本地Bean
        if self.contains_local_bean(name):
            return True

        # 检查父工厂
        if self._parent_bean_factory:
            return self._parent_bean_factory.contains_bean(name)

        return False

    def is_singleton(self, name: str) -> bool:
        """检查Bean是否为单例"""
        if self._registry.has_definition(name):
            bean_definition = self._registry.get_definition(name)
            return bean_definition.singleton()

        # 检查父工厂
        if self._parent_bean_factory:
            return self._parent_bean_factory.is_singleton(name)

        raise KeyError(f"No bean definition found for name: {name}")

    def get_type(self, name: str) -> Optional[type]:
        """获取Bean类型"""
        if self._registry.has_definition(name):
            bean_definition = self._registry.get_definition(name)
            return bean_definition.bean_class

        # 检查父工厂
        if self._parent_bean_factory:
            return self._parent_bean_factory.get_type(name)

        return None

    def get_bean_names(self) -> list[str]:
        """获取所有Bean名称"""
        names = list(self._registry.names())

        # 添加父工厂的Bean名称
        if self._parent_bean_factory and hasattr(self._parent_bean_factory, "get_bean_names"):
            parent_names = self._parent_bean_factory.get_bean_names()
            names.extend(parent_names)

        return list(set(names))  # 去重

    # ===== HierarchicalBeanFactory接口实现 =====

    def get_parent_bean_factory(self) -> Optional[BeanFactory]:
        """获取父Bean工厂"""
        return self._parent_bean_factory

    def contains_local_bean(self, name: str) -> bool:
        """检查本地Bean是否存在（不包括父工厂）"""
        return name in self._singletons or self._registry.has_definition(name)

    # ===== ConfigurableBeanFactory接口实现 =====

    def set_parent_bean_factory(self, parent_bean_factory: Optional[BeanFactory]) -> None:
        """设置父Bean工厂"""
        self._parent_bean_factory = parent_bean_factory

    def register_scope(self, scope_name: str, scope) -> None:
        """注册Bean作用域"""
        self._scope_manager.register_scope(scope_name, scope)

    def add_bean_processor(self, bean_post_processor) -> None:
        """添加Bean后置处理器

        Args:
            bean_post_processor: Bean后置处理器实例
        """
        if bean_post_processor not in self._bean_post_processors:
            self._bean_post_processors.append(bean_post_processor)
            # 按优先级排序（如果处理器有get_order方法）
            self._bean_post_processors.sort(key=lambda p: getattr(p, "get_order", lambda: 0)())

    def get_bean_processor_count(self) -> int:
        """获取Bean后置处理器数量"""
        return len(self._bean_post_processors)

    def get_bean_post_processors(self) -> list[Any]:
        """获取所有Bean后置处理器"""
        return self._bean_post_processors.copy()

    def destroy_singletons(self) -> None:
        """销毁所有单例Bean"""
        with self._lock:
            for name, bean in self._singletons.items():
                try:
                    # 调用销毁方法
                    if hasattr(bean, "destroy"):
                        bean.destroy()
                    logger.debug(f"Destroyed singleton bean: {name}")
                except Exception as e:
                    logger.error(f"Error destroying bean '{name}': {e}")

            # 清空缓存
            self._singletons.clear()
            self._cache_manager.clear()

    # ===== ListableBeanFactory接口实现 =====

    def get_beans_of_type(self, bean_type: type[T], include_non_singletons: bool = True, allow_eager_init: bool = True) -> dict[str, T]:
        """获取指定类型的所有Bean"""
        result = {}

        # 遍历所有Bean定义
        for name in self._registry.names():
            try:
                bean_definition = self._registry.get_definition(name)

                # 检查类型匹配
                if bean_definition.bean_class and issubclass(bean_definition.bean_class, bean_type):
                    # 检查是否包含非单例Bean
                    if not include_non_singletons and not bean_definition.singleton():
                        continue

                    # 获取Bean实例
                    if allow_eager_init or name in self._singletons:
                        bean = self.get_bean(name)
                        if isinstance(bean, bean_type):
                            result[name] = bean

            except Exception as e:
                logger.warning(f"Error getting bean '{name}' of type {bean_type}: {e}")

        return result

    def get_names_for_type(self, bean_type: type, include_non_singletons: bool = True, allow_eager_init: bool = True) -> list[str]:
        """获取指定类型的所有Bean名称"""
        result = []

        # 遍历所有Bean定义
        for name in self._registry.names():
            try:
                bean_definition = self._registry.get_definition(name)

                # 检查类型匹配
                if bean_definition.bean_class and issubclass(bean_definition.bean_class, bean_type):
                    # 检查是否包含非单例Bean
                    if not include_non_singletons and not bean_definition.singleton():
                        continue

                    # 检查是否允许急切初始化
                    if not allow_eager_init and bean_definition.lazy_init:
                        continue

                    result.append(name)

            except Exception as e:
                logger.warning(f"Error checking bean '{name}' for type {bean_type}: {e}")

        return result

    # ===== 工厂管理方法 =====

    def register_bean_definition(self, name: str, bean_definition: BeanDefinition) -> None:
        """注册Bean定义"""
        self._registry.register(name, bean_definition)

    def get_bean_definition(self, name: str) -> BeanDefinition:
        """获取Bean定义"""
        return self._registry.get_definition(name)

    def has_bean_definition(self, name: str) -> bool:
        """检查Bean定义是否存在"""
        return self._registry.has_definition(name)

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        return self._performance_stats.copy()

    @contextmanager
    def bean_creation_context(self, name: str):
        """Bean创建上下文管理器"""
        start_time = time.time()
        try:
            logger.debug(f"Starting creation of bean: {name}")
            yield
        finally:
            creation_time = time.time() - start_time
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
